# 随机抽取系统软件设计说明书

## 文档信息

| 项目名称 | 随机抽取系统 |
|---------|-------------|
| 文档版本 | V1.0 |
| 编写日期 | 2024年1月 |
| 文档类型 | 软件设计说明书 |
| 保密级别 | 内部 |

## 1. 引言

### 1.1 编写目的

本文档为随机抽取系统的详细软件设计说明书，旨在：
- 提供完整的技术架构和实现方案
- 为开发团队提供技术实现指导
- 支撑软件著作权申请
- 为系统维护和扩展提供参考

### 1.2 项目背景

在学术答辩、项目评审等场景中，传统人工分配评委存在效率低、公平性难保证、过程不透明等问题。本系统通过算法保证的随机抽取和公平性控制，实现自动化、透明化的评委分配。

### 1.3 术语定义

| 术语 | 定义 |
|------|------|
| 答辩人 | 需要参加答辩的人员 |
| 评委 | 参与评审的专家 |
| 候选池 | 可参与评审的评委集合 |
| Fisher-Yates算法 | 用于生成随机排列的经典算法 |

## 2. 系统概述

### 2.1 系统定位

专业的评委分配管理工具，具有以下特点：
- 基于Vue.js的Web应用
- 本地数据处理，保证数据安全
- 响应式界面设计
- 算法保证的公平性

### 2.2 核心价值

| 价值维度 | 传统方式 | 本系统 | 改进效果 |
|----------|----------|--------|----------|
| 效率 | 人工分配，耗时长 | 自动化抽取，秒级完成 | 效率提升90%+ |
| 公平性 | 主观判断 | 算法保证，可量化 | 客观可验证 |
| 透明度 | 过程不透明 | 完整日志记录 | 100%可追溯 |

### 2.3 功能架构

```mermaid
graph TB
    subgraph "用户界面层"
        A1[文件上传] --> A2[抽取操作] --> A3[结果展示]
    end
    
    subgraph "业务逻辑层"
        B1[数据管理] --> B2[随机抽取引擎] --> B3[公平性控制]
    end
    
    subgraph "数据层"
        C1[文件解析] --> C2[数据验证] --> C3[内存存储]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    B1 --> C1
    B2 --> C2
    B3 --> C3
```

## 3. 需求分析

### 3.1 功能需求

| 需求ID | 需求描述 | 优先级 |
|--------|----------|--------|
| FR-001 | 支持UTF-8编码文本文件导入 | 高 |
| FR-002 | 实现Fisher-Yates随机抽取算法 | 高 |
| FR-003 | 支持抽取次数限制和公平控制 | 高 |
| FR-004 | 提供直观友好的图形界面 | 高 |
| FR-005 | 完整的操作日志记录 | 中 |

### 3.2 非功能需求

| 性能指标 | 目标值 |
|----------|--------|
| 响应时间 | 单次抽取 < 100ms |
| 数据处理能力 | 支持1000个答辩人 |
| 内存使用 | 运行时 < 50MB |
| 启动时间 | 页面加载 < 2秒 |

## 4. 系统架构设计

### 4.1 总体架构

```mermaid
graph TB
    subgraph "表现层"
        P1[Vue.js组件] --> P2[原生CSS样式]
    end
    
    subgraph "业务层"
        B1[数据管理服务] --> B2[随机抽取引擎] --> B3[公平性控制器]
    end
    
    subgraph "数据层"
        D1[文件读写] --> D2[内存数据管理] --> D3[本地存储]
    end
    
    P1 --> B1
    B1 --> D1
```

### 4.2 技术选型

| 架构层次 | 技术选型 | 版本 | 选择理由 |
|----------|----------|------|----------|
| 前端框架 | Vue.js | 3.2.13 | 响应式设计，组件化开发 |
| 构建工具 | Vue CLI | 5.0.0 | 快速构建，开发便利 |
| 样式 | 原生CSS | - | 轻量级，无额外依赖 |
| 编程语言 | JavaScript ES6+ | 最新 | 现代语法特性 |

## 5. 详细设计

### 5.1 数据模型设计

```javascript
// 系统数据结构
data() {
    return {
        num: 4,                    // 最大抽取次数限制
        defenderData: {},          // 答辩人数据：{答辩人: [评委列表]}
        expertDict: {},            // 评委计数：{评委: 被抽取次数}
        resultContent: '',         // 抽取结果显示
        logs: [],                  // 操作日志数组
        isShowLog: false,          // 日志显示状态
        logContent: ''             // 日志内容字符串
    }
}
```

### 5.2 核心算法实现

#### 5.2.1 Fisher-Yates洗牌算法

```javascript
getRandomKeys(array, num) {
    const shuffled = array.slice();
    let i = array.length;
    let temp;
    let randomIndex;

    // Fisher-Yates洗牌算法实现
    while (i--) {
        randomIndex = Math.floor((i + 1) * Math.random());
        temp = shuffled[randomIndex];
        shuffled[randomIndex] = shuffled[i];
        shuffled[i] = temp;
    }

    return shuffled.slice(0, num);
}
```

#### 5.2.2 文件解析算法

```javascript
handleMapFileChange(event) {
    const file = event.target.files[0];
    if (!file) {
        alert('请选择文件');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = () => {
        try {
            const content = reader.result;
            const lines = content.split('\n');
            const allExperts = new Set();
            const newDefenderData = {};
            
            lines.forEach(line => {
                const trimmedLine = line.trim();
                if (trimmedLine) {
                    const parts = trimmedLine.split('，');
                    if (parts.length >= 2) {
                        const defenderName = parts[0].trim();
                        const experts = parts.slice(1).map(expert => expert.trim()).filter(expert => expert);
                        
                        if (defenderName && experts.length > 0) {
                            newDefenderData[defenderName] = experts;
                            experts.forEach(expert => allExperts.add(expert));
                        }
                    }
                }
            });
            
            // 更新数据
            this.defenderData = newDefenderData;
            this.expertDict = {};
            allExperts.forEach(expert => {
                this.expertDict[expert] = 0;
            });
            
            alert(`数据加载成功！共${Object.keys(newDefenderData).length}个答辩人，${allExperts.size}个评委`);
        } catch (error) {
            alert('文件解析失败，请检查文件格式');
        }
    };
    
    reader.readAsText(file, 'UTF-8');
}
```

#### 5.2.3 随机抽取核心逻辑

```javascript
handleButtonClick(defenderName) {
    const candidateExperts = this.defenderData[defenderName];
    if (!candidateExperts || candidateExperts.length === 0) {
        alert('该答辩人没有可用的评委');
        return;
    }
    
    // 筛选可用评委（考虑次数限制）
    const availableExperts = candidateExperts.filter(expert => 
        this.expertDict[expert] < this.num
    );
    
    // 随机抽取3个评委
    const selectedExperts = this.getRandomKeys(availableExperts, 3);
    this.resultContent = selectedExperts.join('、');
    
    // 更新评委计数
    selectedExperts.forEach(expert => {
        this.expertDict[expert] += 1;
    });
    
    // 记录日志
    let log = `${new Date().toLocaleString()}, 答辩人:${defenderName}, 评委:${this.resultContent}`;
    this.logContent = this.logContent + log + '\n';
}
```

## 6. 用户界面设计

### 6.1 界面架构

```mermaid
graph TD
    A[App.vue] --> B[RandomPicker.vue]
    B --> C[设置面板]
    B --> D[答辩人列表]
    B --> E[评委网格]
    B --> F[结果显示]
    B --> G[日志模态框]
```

### 6.2 交互流程

```mermaid
stateDiagram-v2
    [*] --> 初始状态
    初始状态 --> 数据加载中: 上传文件
    数据加载中 --> 数据就绪: 加载成功
    数据就绪 --> 抽取中: 点击答辩人
    抽取中 --> 结果显示: 抽取完成
    结果显示 --> 数据就绪: 继续操作
```

### 6.3 样式设计特点

- **响应式布局**：使用CSS Grid和Flexbox
- **现代化设计**：圆角、阴影、渐变效果
- **状态反馈**：不同状态的视觉提示
- **交互友好**：悬停效果和动画过渡

## 7. 安全性设计

### 7.1 数据安全

- **本地处理**：所有数据仅在浏览器本地处理
- **输入验证**：严格的文件类型和内容验证
- **编码支持**：UTF-8编码确保中文字符正确处理

### 7.2 输入验证

```javascript
// 文件类型验证
if (!file.type.includes('text')) {
    alert('请选择文本文件');
    return;
}

// 内容格式验证
const parts = trimmedLine.split('，');
if (parts.length >= 2) {
    // 验证格式正确性
}
```

## 8. 测试策略

### 8.1 功能测试

- **文件上传测试**：各种格式文件的兼容性
- **算法测试**：随机性和公平性验证
- **界面测试**：用户交互和状态更新
- **边界测试**：极限数据量处理

### 8.2 性能测试

- **响应时间测试**：各操作的响应速度
- **内存使用测试**：长时间运行的稳定性
- **并发测试**：快速连续操作的处理

## 9. 部署和维护

### 9.1 构建流程

```bash
# 开发环境
npm run serve

# 生产构建
npm run build

# 代码检查
npm run lint
```

### 9.2 版本管理

当前版本：v0.1.0
- 基础功能完整实现
- 支持文件导入和随机抽取
- 完整的日志记录功能

## 10. 创新点和技术亮点

### 10.1 核心创新

1. **智能公平性控制**：通过次数限制确保评委工作负载均衡
2. **简洁高效架构**：单组件设计，降低复杂度
3. **用户友好界面**：直观的操作流程和状态反馈
4. **完整审计功能**：详细的操作日志记录

### 10.2 算法优势

```mermaid
graph LR
    A[传统随机] --> A1[分布不均]
    B[Fisher-Yates算法] --> B1[真随机性]
    B1 --> B2[公平性保证]
    B2 --> B3[次数控制]
```

### 10.3 技术特色

- 基于Vue.js的响应式设计
- Fisher-Yates洗牌算法保证随机性
- 完整的操作审计和日志管理
- 轻量级架构，易于维护和扩展

## 11. 结论

本随机抽取系统通过简洁高效的架构设计、可靠的算法实现和友好的用户界面，成功解决了答辩评委分配的实际问题。系统具有良好的可维护性和扩展性，为类似场景的应用提供了参考价值。

### 系统特色总结

| 特色功能 | 技术实现 | 创新价值 |
|----------|----------|----------|
| 智能随机抽取 | Fisher-Yates算法 | 保证数学意义上的真随机性 |
| 公平性控制 | 次数限制机制 | 确保评委工作负载均衡 |
| 响应式界面 | Vue.js + CSS Grid | 适配不同设备，提升用户体验 |
| 完整审计 | 结构化日志记录 | 支持操作追溯和合规审计 |

---

**开发信息**
- 开发单位：信息技术中心
- 开发人员：丁新
- 完成时间：2024年
- 版本：V1.0



