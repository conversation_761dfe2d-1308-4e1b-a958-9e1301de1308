<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随机抽取系统软件设计说明书</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.8;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .container {
            background: white;
            padding: 50px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            border-bottom: 3px solid #007acc;
            padding-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .doc-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .doc-info table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .doc-info td {
            padding: 8px 15px;
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .doc-info td:first-child {
            font-weight: bold;
            background: rgba(255,255,255,0.1);
        }
        
        h2 {
            color: #2c3e50;
            border-left: 4px solid #007acc;
            padding-left: 15px;
            margin-top: 40px;
            margin-bottom: 20px;
        }
        
        h3 {
            color: #34495e;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }
        
        .mermaid {
            text-align: center;
            margin: 30px 0;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            position: relative;
         
           position: relative;
        .mermaid-container { 
             osition:  elative;
            display: inlin -block;
        }
        
        .download-iconpo
            position: absolute;
            top: 10px;sition: relative;
        .merright: 10px;
            maid-connd: rgba(0, 0, 0, 0.7);
            color: white;
            border: tone;
            border-radius: 50%;
            wiath: 35px;
            heighti 35px;
      n     cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }
        
        .download-icon:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }
        
        pre {
            background: er {}
            osition: elative;
            display: inlin-block;
        }
        
        .download-icon
            position: absolute;
            top: 10px;
        .merright: 10px;
            maid-connd: rgba(0, 0, 0, 0.7);
            color: white;
            border: tone;
            border-radius: 50%;
            wiath: 35px;
            heighti 35px;
      n     cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }
        
        .download-icon:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }
        
        pre {
            background: er {
            position: relative;
            display: inline-block;
        }
        
        .download-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }
        
        .download-icon:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }
        
        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            overflow-x: auto;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        code {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .highlight {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 25px;
            border-radius: 10px;
            margin: 30px 0;
            border-left: 5px solid #e17055;
        }
        
        .innovation {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .innovation ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .innovation li {
            margin: 10px 0;
            line-height: 1.6;
        }
        
        ul, ol {
            padding-left: 25px;
        }
        
        li {
            margin: 8px 0;
        }
        
        .export-buttons {
            text-align: center;
            margin: 30px 0;
        }
        
        .export-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        @media print {
            body { background: white; }
            .container { box-shadow: none; }
            .export-buttons { display: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>随机抽取系统软件设计说明书</h1>
            
            <div class="doc-info">
                <table>
                    <tr><td>项目名称</td><td>随机抽取系统</td></tr>
                    <tr><td>文档版本</td><td>V1.0</td></tr>
                    <tr><td>编写日期</td><td>2024年1月</td></tr>
                    <tr><td>文档类型</td><td>软件设计说明书</td></tr>
                    <tr><td>保密级别</td><td>内部</td></tr>
                </table>
            </div>
            
            <div class="export-buttons">
                <button class="export-btn" onclick="exportToPDF()">导出PDF</button>
                <button class="export-btn" onclick="exportToWord()">导出Word</button>
                <button class="export-btn" onclick="downloadImages()">下载图片</button>
            </div>
        </div>

        <section id="introduction">
            <h2>1. 引言</h2>
            
            <h3>1.1 编写目的</h3>
            <p>本文档为随机抽取系统的详细软件设计说明书，旨在：</p>
            <ul>
                <li>提供完整的技术架构和实现方案</li>
                <li>为开发团队提供技术实现指导</li>
                <li>支撑软件著作权申请</li>
                <li>为系统维护和扩展提供参考</li>
            </ul>
            
            <h3>1.2 项目背景</h3>
            <p>在学术答辩、项目评审等场景中，传统人工分配评委存在效率低、公平性难保证、过程不透明等问题。本系统通过算法保证的随机抽取和公平性控制，实现自动化、透明化的评委分配。</p>
            
            <h3>1.3 术语定义</h3>
            <table>
                <tr>
                    <th>术语</th>
                    <th>定义</th>
                </tr>
                <tr>
                    <td>答辩人</td>
                    <td>需要参加答辩的人员</td>
                </tr>
                <tr>
                    <td>评委</td>
                    <td>参与评审的专家</td>
                </tr>
                <tr>
                    <td>候选池</td>
                    <td>可参与评审的评委集合</td>
                </tr>
                <tr>
                    <td>Fisher-Yates算法</td>
                    <td>用于生成随机排列的经典算法</td>
                </tr>
            </table>
        </section>

        <section id="overview">
            <h2>2. 系统概述</h2>
            
            <h3>2.1 系统定位</h3>
            <p>专业的评委分配管理工具，具有以下特点：</p>
            <ul>
                <li>基于Vue.js的Web应用</li>
                <li>本地数据处理，保证数据安全</li>
                <li>响应式界面设计</li>
                <li>算法保证的公平性</li>
            </ul>
            
            <h3>2.2 核心价值</h3>
            <table>
                <tr>
                    <th>价值维度</th>
                    <th>传统方式</th>
                    <th>本系统</th>
                    <th>改进效果</th>
                </tr>
                <tr>
                    <td>效率</td>
                    <td>人工分配，耗时长</td>
                    <td>自动化抽取，秒级完成</td>
                    <td>效率提升90%+</td>
                </tr>
                <tr>
                    <td>公平性</td>
                    <td>主观判断</td>
                    <td>算法保证，可量化</td>
                    <td>客观可验证</td>
                </tr>
                <tr>
                    <td>透明度</td>
                    <td>过程不透明</td>
                    <td>完整日志记录</td>
                    <td>100%可追溯</td>
                </tr>
            </table>
            
            <h3>2.3 功能架构</h3>
            <div class="mermaid">
graph TB
    subgraph "用户界面层"
        A1[文件上传] --> A2[抽取操作] --> A3[结果展示]
    end
    
    subgraph "业务逻辑层"
        B1[数据管理] --> B2[随机抽取引擎] --> B3[公平性控制]
    end
    
    subgraph "数据层"
        C1[文件解析] --> C2[数据验证] --> C3[内存存储]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    B1 --> C1
    B2 --> C2
    B3 --> C3
            </div>
        </section>

        <section id="requirements">
            <h2>3. 需求分析</h2>
            
            <h3>3.1 功能需求</h3>
            <table>
                <tr>
                    <th>需求ID</th>
                    <th>需求描述</th>
                    <th>优先级</th>
                </tr>
                <tr>
                    <td>FR-001</td>
                    <td>支持UTF-8编码文本文件导入</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>FR-002</td>
                    <td>实现Fisher-Yates随机抽取算法</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>FR-003</td>
                    <td>支持抽取次数限制和公平控制</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>FR-004</td>
                    <td>提供直观友好的图形界面</td>
                    <td>高</td>
                </tr>
                <tr>
                    <td>FR-005</td>
                    <td>完整的操作日志记录</td>
                    <td>中</td>
                </tr>
            </table>
            
            <h3>3.2 非功能需求</h3>
            <table>
                <tr>
                    <th>性能指标</th>
                    <th>目标值</th>
                </tr>
                <tr>
                    <td>响应时间</td>
                    <td>单次抽取 < 100ms</td>
                </tr>
                <tr>
                    <td>数据处理能力</td>
                    <td>支持1000个答辩人</td>
                </tr>
                <tr>
                    <td>内存使用</td>
                    <td>运行时 < 50MB</td>
                </tr>
                <tr>
                    <td>启动时间</td>
                    <td>页面加载 < 2秒</td>
                </tr>
            </table>
        </section>

        <section id="architecture">
            <h2>4. 系统架构设计</h2>
            
            <h3>4.1 总体架构</h3>
            <div class="mermaid">
graph TB
    subgraph "表现层"
        P1[Vue.js组件] --> P2[原生CSS样式]
    end
    
    subgraph "业务层"
        B1[数据管理服务] --> B2[随机抽取引擎] --> B3[公平性控制器]
    end
    
    subgraph "数据层"
        D1[文件读写] --> D2[内存数据管理] --> D3[本地存储]
    end
    
    P1 --> B1
    B1 --> D1
            </div>
            
            <h3>4.2 技术选型</h3>
            <table>
                <tr>
                    <th>架构层次</th>
                    <th>技术选型</th>
                    <th>版本</th>
                    <th>选择理由</th>
                </tr>
                <tr>
                    <td>前端框架</td>
                    <td>Vue.js</td>
                    <td>3.2.13</td>
                    <td>响应式设计，组件化开发</td>
                </tr>
                <tr>
                    <td>构建工具</td>
                    <td>Vue CLI</td>
                    <td>5.0.0</td>
                    <td>快速构建，开发便利</td>
                </tr>
                <tr>
                    <td>样式</td>
                    <td>原生CSS</td>
                    <td>-</td>
                    <td>轻量级，无额外依赖</td>
                </tr>
                <tr>
                    <td>编程语言</td>
                    <td>JavaScript ES6+</td>
                    <td>最新</td>
                    <td>现代语法特性</td>
                </tr>
            </table>
        </section>

        <section id="detailed-design">
            <h2>5. 详细设计</h2>
            
            <h3>5.1 数据模型设计</h3>
            <pre><code class="language-javascript">// 系统数据结构
data() {
    return {
        num: 4,                    // 最大抽取次数限制
        defenderData: {},          // 答辩人数据：{答辩人: [评委列表]}
        expertDict: {},            // 评委计数：{评委: 被抽取次数}
        resultContent: '',         // 抽取结果显示
        logs: [],                  // 操作日志数组
        isShowLog: false,          // 日志显示状态
        logContent: ''             // 日志内容字符串
    }
}</code></pre>
            
            <h3>5.2 核心算法实现</h3>
            
            <h4>5.2.1 Fisher-Yates洗牌算法</h4>
            <pre><code class="language-javascript">getRandomKeys(array, num) {
    const shuffled = array.slice();
    let i = array.length;
    let temp;
    let randomIndex;

    // Fisher-Yates洗牌算法实现
    while (i--) {
        randomIndex = Math.floor((i + 1) * Math.random());
        temp = shuffled[randomIndex];
        shuffled[randomIndex] = shuffled[i];
        shuffled[i] = temp;
    }

    return shuffled.slice(0, num);
}</code></pre>
            
            <h4>5.2.2 文件解析算法</h4>
            <pre><code class="language-javascript">handleMapFileChange(event) {
    const file = event.target.files[0];
    if (!file) {
        alert('请选择文件');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = () => {
        try {
            const content = reader.result;
            const lines = content.split('\n');
            const allExperts = new Set();
            const newDefenderData = {};
            
            lines.forEach(line => {
                const trimmedLine = line.trim();
                if (trimmedLine) {
                    const parts = trimmedLine.split('，');
                    if (parts.length >= 2) {
                        const defenderName = parts[0].trim();
                        const experts = parts.slice(1).map(expert => expert.trim()).filter(expert => expert);
                        
                        if (defenderName && experts.length > 0) {
                            newDefenderData[defenderName] = experts;
                            experts.forEach(expert => allExperts.add(expert));
                        }
                    }
                }
            });
            
            // 更新数据
            this.defenderData = newDefenderData;
            this.expertDict = {};
            allExperts.forEach(expert => {
                this.expertDict[expert] = 0;
            });
            
            alert(`数据加载成功！共${Object.keys(newDefenderData).length}个答辩人，${allExperts.size}个评委`);
        } catch (error) {
            alert('文件解析失败，请检查文件格式');
        }
    };
    
    reader.readAsText(file, 'UTF-8');
}</code></pre>
            
            <h4>5.2.3 随机抽取核心逻辑</h4>
            <pre><code class="language-javascript">handleButtonClick(defenderName) {
    const candidateExperts = this.defenderData[defenderName];
    if (!candidateExperts || candidateExperts.length === 0) {
        alert('该答辩人没有可用的评委');
        return;
    }
    
    // 筛选可用评委（考虑次数限制）
    const availableExperts = candidateExperts.filter(expert => 
        this.expertDict[expert] < this.num
    );
    
    // 随机抽取3个评委
    const selectedExperts = this.getRandomKeys(availableExperts, 3);
    this.resultContent = selectedExperts.join('、');
    
    // 更新评委计数
    selectedExperts.forEach(expert => {
        this.expertDict[expert] += 1;
    });
    
    // 记录日志
    let log = `${new Date().toLocaleString()}, 答辩人:${defenderName}, 评委:${this.resultContent}`;
    this.logContent = this.logContent + log + '\n';
}</code></pre>
        </section>

        <section id="ui-design">
            <h2>6. 用户界面设计</h2>
            
            <h3>6.1 界面架构</h3>
            <div class="mermaid">
graph TD
    A[App.vue] --> B[RandomPicker.vue]
    B --> C[设置面板]
    B --> D[答辩人列表]
    B --> E[评委网格]
    B --> F[结果显示]
    B --> G[日志模态框]
            </div>
            
            <h3>6.2 交互流程</h3>
            <div class="mermaid">
stateDiagram-v2
    [*] --> 初始状态
    初始状态 --> 数据加载中: 上传文件
    数据加载中 --> 数据就绪: 加载成功
    数据就绪 --> 抽取中: 点击答辩人
    抽取中 --> 结果显示: 抽取完成
    结果显示 --> 数据就绪: 继续操作
            </div>
            
            <h3>6.3 样式设计特点</h3>
            <ul>
                <li><strong>响应式布局</strong>：使用CSS Grid和Flexbox</li>
                <li><strong>现代化设计</strong>：圆角、阴影、渐变效果</li>
                <li><strong>状态反馈</strong>：不同状态的视觉提示</li>
                <li><strong>交互友好</strong>：悬停效果和动画过渡</li>
            </ul>
        </section>

        <section id="security">
            <h2>7. 安全性设计</h2>
            
            <h3>7.1 数据安全</h3>
            <ul>
                <li><strong>本地处理</strong>：所有数据仅在浏览器本地处理</li>
                <li><strong>输入验证</strong>：严格的文件类型和内容验证</li>
                <li><strong>编码支持</strong>：UTF-8编码确保中文字符正确处理</li>
            </ul>
            
            <h3>7.2 输入验证</h3>
            <pre><code class="language-javascript">// 文件类型验证
if (!file.type.includes('text')) {
    alert('请选择文本文件');
    return;
}

// 内容格式验证
const parts = trimmedLine.split('，');
if (parts.length >= 2) {
    // 验证格式正确性
}</code></pre>
        </section>

        <section id="testing">
            <h2>8. 测试策略</h2>
            
            <h3>8.1 功能测试</h3>
            <ul>
                <li><strong>文件上传测试</strong>：各种格式文件的兼容性</li>
                <li><strong>算法测试</strong>：随机性和公平性验证</li>
                <li><strong>界面测试</strong>：用户交互和状态更新</li>
                <li><strong>边界测试</strong>：极限数据量处理</li>
            </ul>
            
            <h3>8.2 性能测试</h3>
            <ul>
                <li><strong>响应时间测试</strong>：各操作的响应速度</li>
                <li><strong>内存使用测试</strong>：长时间运行的稳定性</li>
                <li><strong>并发测试</strong>：快速连续操作的处理</li>
            </ul>
        </section>

        <section id="deployment">
            <h2>9. 部署和维护</h2>
            
            <h3>9.1 构建流程</h3>
            <pre><code class="language-bash"># 开发环境
npm run serve

# 生产构建
npm run build

# 代码检查
npm run lint</code></pre>
            
            <h3>9.2 版本管理</h3>
            <p>当前版本：v0.1.0</p>
            <ul>
                <li>基础功能完整实现</li>
                <li>支持文件导入和随机抽取</li>
                <li>完整的日志记录功能</li>
            </ul>
        </section>

        <section id="innovation">
            <h2>10. 创新点和技术亮点</h2>
            
            <h3>10.1 核心创新</h3>
            <div class="innovation">
                <ol>
                    <li><strong>智能公平性控制</strong>：通过次数限制确保评委工作负载均衡</li>
                    <li><strong>简洁高效架构</strong>：单组件设计，降低复杂度</li>
                    <li><strong>用户友好界面</strong>：直观的操作流程和状态反馈</li>
                    <li><strong>完整审计功能</strong>：详细的操作日志记录</li>
                </ol>
            </div>
            
            <h3>10.2 算法优势</h3>
            <div class="mermaid">
graph LR
    A[传统随机] --> A1[分布不均]
    B[Fisher-Yates算法] --> B1[真随机性]
    B1 --> B2[公平性保证]
    B2 --> B3[次数控制]
            </div>
            
            <h3>10.3 技术特色</h3>
            <ul>
                <li>基于Vue.js的响应式设计</li>
                <li>Fisher-Yates洗牌算法保证随机性</li>
                <li>完整的操作审计和日志管理</li>
                <li>轻量级架构，易于维护和扩展</li>
            </ul>
        </section>

        <section id="conclusion">
            <h2>11. 结论</h2>
            
            <p>本随机抽取系统通过简洁高效的架构设计、可靠的算法实现和友好的用户界面，成功解决了答辩评委分配的实际问题。系统具有良好的可维护性和扩展性，为类似场景的应用提供了参考价值。</p>
            
            <div class="highlight">
                <h3>系统特色总结</h3>
                <table>
                    <tr>
                        <th>特色功能</th>
                        <th>技术实现</th>
                        <th>创新价值</th>
                    </tr>
                    <tr>
                        <td>智能随机抽取</td>
                        <td>Fisher-Yates算法</td>
                        <td>保证数学意义上的真随机性</td>
                    </tr>
                    <tr>
                        <td>公平性控制</td>
                        <td>次数限制机制</td>
                        <td>确保评委工作负载均衡</td>
                    </tr>
                    <tr>
                        <td>响应式界面</td>
                        <td>Vue.js + CSS Grid</td>
                        <td>适配不同设备，提升用户体验</td>
                    </tr>
                    <tr>
                        <td>完整审计</td>
                        <td>结构化日志记录</td>
                        <td>支持操作追溯和合规审计</td>
                    </tr>
                </table>
            </div>
            
            <hr style="margin: 40px 0;">
            
            <div style="text-align: center; color: #666;">
                <p><strong>开发信息</strong></p>
                <p>开发单位：信息技术中心</p>
                <p>开发人员：丁新</p>
                <p>完成时间：2024年</p>
                <p>版本：V1.0</p>
            </div>
        </section>
    </div>

    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
        
        // 页面加载完成后添加下载按钮
        document.addEventListener('DOMContentLoaded', function() {
            // 等待更长时间确保Mermaid完全渲染
            setTimeout(() => {
                addDownloadButtons();
            }, 3000);
        });
        
        // 为每个图表添加下载按钮
        function addDownloadButtons() {
            const mermaidElements = document.querySelectorAll('.mermaid');
            console.log('找到图表数量:', mermaidElements.length);
            
            mermaidElements.forEach((element, index) => {
                // 检查是否已经有下载按钮
                if (element.querySelector('.download-icon')) {
                    return;
                }
                
                // 创建下载按钮
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-icon';
                downloadBtn.innerHTML = '⬇️';
                downloadBtn.title = '下载此图片';
                downloadBtn.onclick = (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    downloadSingleImage(element, index);
                };
                
                element.appendChild(downloadBtn);
                console.log(`为图表 ${index + 1} 添加了下载按钮`);
            });
        }
        
        // 下载单个图片 - 简化版本
        function downloadSingleImage(mermaidElement, index) {
            try {
                const svg = mermaidElement.querySelector('svg');
                if (!svg) {
                    alert('未找到SVG图片！');
                    return;
                }
                
                console.log('开始下载图片:', index + 1);
                
                // 获取SVG内容
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
                
                // 直接下载SVG文件
                const downloadLink = document.createElement('a');
                downloadLink.href = URL.createObjectURL(svgBlob);
                downloadLink.download = `系统架构图_${index + 1}.svg`;
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                URL.revokeObjectURL(downloadLink.href);
                
                console.log('SVG下载完成');
                
                // 同时尝试转换为PNG
                convertSvgToPng(svg, index);
                
            } catch (error) {
                console.error('下载失败:', error);
                alert('下载失败，请重试');
            }
        }
        
        // 转换SVG为PNG
        function convertSvgToPng(svg, index) {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                // 设置canvas尺寸
                const bbox = svg.getBBox ? svg.getBBox() : {width: 800, height: 600};
                canvas.width = bbox.width || 800;
                canvas.height = bbox.height || 600;
                
                // 创建SVG数据URL
                const svgData = new XMLSerializer().serializeToString(svg);
                const svgUrl = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
                
                img.onload = function() {
                    // 白色背景
                    ctx.fillStyle = 'white';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // 绘制图片
                    ctx.drawImage(img, 0, 0);
                    
                    // 下载PNG
                    canvas.toBlob(function(blob) {
                        if (blob) {
                            const link = document.createElement('a');
                            link.href = URL.createObjectURL(blob);
                            link.download = `系统架构图_${index + 1}.png`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            URL.revokeObjectURL(link.href);
                            console.log('PNG下载完成');
                        }
                    }, 'image/png', 1.0);
                };
                
                img.onerror = function() {
                    console.log('PNG转换失败，但SVG已下载');
                };
                
                img.src = svgUrl;
                
            } catch (error) {
                console.error('PNG转换失败:', error);
            }
        }
        
        // 批量下载 - 简化版本
        function downloadImages() {
            const mermaidElements = document.querySelectorAll('.mermaid');
            
            if (mermaidElements.length === 0) {
                alert('未找到可下载的图片！');
                return;
            }
            
            console.log('开始批量下载，共', mermaidElements.length, '张图片');
            
            mermaidElements.forEach((element, index) => {
                // 延迟下载，避免浏览器阻止多个下载
                setTimeout(() => {
                    downloadSingleImage(element, index);
                }, index * 500);
            });
            
            alert(`开始下载 ${mermaidElements.length} 张图片，请稍候...`);
        }
        
        // 导出PDF功能占位
        function exportToPDF() {
            alert('PDF导出功能开发中...');
        }
        
        // 导出Word功能占位
        function exportToWord() {
            alert('Word导出功能开发中...');
        }
    </script>
</body>
</html>





