<template>
    <div class="random-picker">
        <!-- 头部卡片 -->
        <div class="card header-card">
            <div class="header-content">
                <h1 class="page-title">随机抽取系统</h1>
                <div class="action-buttons">
                    <button @click="resetCount" class="action-btn reset-btn">
                        <i class="icon">🔄</i>
                        计数复位
                    </button>
                    <button @click="isShowLog = true" class="action-btn log-btn">
                        <i class="icon">📋</i>
                        查看日志
                    </button>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <!-- 设置卡片 -->
                <div class="card settings-card">
                    <h3 class="card-title">
                        <i class="icon">⚙️</i>
                        系统设置
                    </h3>
                    <div class="settings-content">
                        <div class="setting-item">
                            <label class="setting-label">
                                <i class="icon">🎯</i>
                                最多被抽取次数
                            </label>
                            <input 
                                type="number" 
                                v-model="num" 
                                min="1" 
                                max="10" 
                                class="number-input"
                            />
                        </div>
                        <div class="setting-item">
                            <label class="setting-label">
                                <i class="icon">📁</i>
                                答辩人名单
                            </label>
                            <div class="file-upload">
                                <input 
                                    type="file" 
                                    @change="handleMapFileChange" 
                                    id="defender-file" 
                                    accept=".txt"
                                    class="file-input"
                                />
                                <label for="defender-file" class="file-label">
                                    <i class="icon">📤</i>
                                    选择文件
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 答辩人列表卡片 -->
                <div class="card defender-card">
                    <h3 class="card-title">
                        <i class="icon">👥</i>
                        答辩人列表
                    </h3>
                    <div class="defender-list" v-if="hasDefenderData">
                        <button 
                            v-for="key in Object.keys(defenderData)" 
                            :key="key"
                            @click="handleButtonClick(key)"
                            class="defender-btn"
                        >
                            <i class="icon">🎲</i>
                            {{ key }}
                        </button>
                    </div>
                    <div v-else class="empty-state">
                        <i class="icon">📝</i>
                        <p>请先上传答辩人名单</p>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div class="right-panel">
                <!-- 评委和结果卡片 -->
                <div class="card result-card">
                    <h3 class="card-title">
                        <i class="icon">👨‍⚖️</i>
                        评委名单
                    </h3>
                    <div class="expert-list">
                        <span 
                            v-for="(value, key) in expertDict" 
                            :key="key"
                            :class="{ 'expert-overlimit': value >= num }"
                            class="expert-item"
                        >
                            {{ key }}
                            <span class="expert-count">({{ value }})</span>
                        </span>
                    </div>
                    
                    <div class="result-section">
                        <h3 class="result-title">
                            <i class="icon">🎉</i>
                            抽签结果
                        </h3>
                        <div class="result-display" :class="{ 'has-result': resultContent }">
                            <div v-if="resultContent" class="result-content">
                                <i class="icon">✅</i>
                                <span class="result-text">{{ resultContent }}</span>
                            </div>
                            <div v-else class="waiting-text">
                                <i class="icon">⏳</i>
                                <span>等待抽取...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志对话框 -->
        <div v-if="isShowLog" class="modal-overlay" @click="isShowLog = false">
            <div class="modal-content" @click.stop>
                <div class="modal-header">
                    <h3>操作日志</h3>
                    <button @click="isShowLog = false" class="close-btn">×</button>
                </div>
                <div class="modal-body">
                    <textarea 
                        v-model="logContent"
                        readonly
                        class="log-textarea"
                        placeholder="日志内容"
                    ></textarea>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p>Produced by 信息技术中心 丁新</p>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            num: 4,
            defenderData: {}, // 答辩人数据：{答辩人: [评委列表]}
            expertDict: {}, // 评委计数：{评委: 被抽取次数}
            resultContent: '',
            logs: [],
            isShowLog: false,
            logContent: '',
        }
    },
    methods: {
        handleMapFileChange(event) {
            const file = event.target.files[0];
            if (!file) {
                alert('请选择文件');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = () => {
                try {
                    const content = reader.result;
                    const lines = content.split('\n');
                    const allExperts = new Set(); // 用于收集所有评委
                    const newDefenderData = {}; // 临时存储新的答辩人数据
                    
                    lines.forEach(line => {
                        const trimmedLine = line.trim();
                        if (trimmedLine) {
                            const parts = trimmedLine.split('，');
                            if (parts.length >= 2) {
                                const defenderName = parts[0].trim();
                                const experts = parts.slice(1).map(expert => expert.trim()).filter(expert => expert);
                                
                                if (defenderName && experts.length > 0) {
                                    // 存储答辩人的评委列表
                                    newDefenderData[defenderName] = experts;
                                    
                                    // 收集所有评委到全集
                                    experts.forEach(expert => allExperts.add(expert));
                                }
                            }
                        }
                    });
                    
                    // 更新答辩人数据
                    this.defenderData = newDefenderData;
                    
                    // 初始化评委计数
                    const newExpertDict = {};
                    allExperts.forEach(expert => {
                        newExpertDict[expert] = 0;
                    });
                    this.expertDict = newExpertDict;
                    
                    console.log('答辩人数据:', this.defenderData);
                    console.log('评委全集:', Array.from(allExperts));
                    
                    alert(`成功加载 ${Object.keys(this.defenderData).length} 个答辩人的数据`);
                } catch (error) {
                    console.error('文件解析错误:', error);
                    alert('文件格式错误，请检查文件内容');
                }
            };
            
            reader.onerror = () => {
                alert('文件读取失败');
            };
            
            reader.readAsText(file, 'UTF-8');
        },
        handleButtonClick(defenderName) {
            const candidateExperts = this.defenderData[defenderName];
            if (!candidateExperts || candidateExperts.length === 0) {
                alert('该答辩人没有可用的评委');
                return;
            }
            
            // 从候选评委中筛选可用评委（考虑次数限制）
            const availableExperts = candidateExperts.filter(expert => 
                this.expertDict[expert] < this.num
            );
            
            // if (availableExperts.length === 0) {
            //     alert('该答辩人的所有评委都已达到抽取次数限制');
            //     return;
            // }
            
            // 随机抽取3个评委
            const selectedExperts = this.getRandomKeys(availableExperts, 3);
            // 保持随机顺序显示
            this.resultContent = selectedExperts.join('、');
            
            // 更新评委计数
            selectedExperts.forEach(expert => {
                this.expertDict[expert] += 1;
            });
            
            // 记录日志
            let log = `${new Date().toLocaleString()}, 答辩人:${defenderName}, 评委:${this.resultContent}`;
            this.logContent = this.logContent + log + '\n';
        },
        getRandomKeys(array, num) {
            const shuffled = array.slice();
            let i = array.length;
            let temp;
            let randomIndex;

            while (i--) {
                randomIndex = Math.floor((i + 1) * Math.random());
                temp = shuffled[randomIndex];
                shuffled[randomIndex] = shuffled[i];
                shuffled[i] = temp;
            }

            return shuffled.slice(0, num);
        },
        resetCount() {
            Object.keys(this.expertDict).forEach(key => {
                this.expertDict[key] = 0;
            });
            this.resultContent = '';
            alert('计数已复位');
        },
    },
    computed: {
        hasDefenderData() {
            return Object.keys(this.defenderData).length > 0;
        }
    }
};
</script>

<style scoped>
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.random-picker {
    min-height: 97vh;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 25%, #e8f5e8 50%, #fff3e0 75%, #fce4ec 100%);
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 卡片基础样式 */
.card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding: 24px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 头部卡片 */
.header-card {
    background: linear-gradient(135deg, #42a5f5 0%, #64b5f6 50%, #90caf9 100%);
    color: white;
    text-align: center;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-buttons {
    display: flex;
    gap: 12px;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 20px;
    flex: 1;
}

/* 左侧面板 */
.left-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 设置卡片 */
.settings-card {
    background: #f9fafb;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #b6d2ff;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.setting-label {
    font-weight: 500;
    color: #6b7280;
    min-width: 120px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.number-input {
    width: 80px;
    padding: 8px 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.number-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 文件上传 */
.file-upload {
    position: relative;
    flex: 1;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: linear-gradient(135deg, #42a5f5 0%, #64b5f6 100%);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    justify-content: center;
}

.file-label:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 165, 245, 0.3);
}

/* 答辩人卡片 */
.defender-card {
    flex: 1;
    min-height: 200px;
}

.defender-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
    margin-top: 16px;
    max-height: 300px;
    overflow-y: auto;
    padding-right: 8px;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
}

.defender-list::-webkit-scrollbar {
    width: 6px;
}

.defender-list::-webkit-scrollbar-track {
    background: #f7fafc;
    border-radius: 3px;
}

.defender-list::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

.defender-list::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

.defender-btn {
    background: linear-gradient(135deg, #42a5f5 0%, #64b5f6 100%);
    color: white;
    border: none;
    padding: 12px 8px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    min-height: 44px;
}

.defender-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(66, 165, 245, 0.4);
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #9ca3af;
}

.empty-state .icon {
    font-size: 3rem;
    margin-bottom: 12px;
    display: block;
}

/* 右侧面板 */
.right-panel {
    display: flex;
    flex-direction: column;
}

/* 结果卡片 */
.result-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.expert-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 24px;
    padding: 16px;
    background: #f9fafb;
    border-radius: 8px;
    max-height: 120px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
}

.expert-list::-webkit-scrollbar {
    width: 6px;
}

.expert-list::-webkit-scrollbar-track {
    background: #f7fafc;
    border-radius: 3px;
}

.expert-list::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

.expert-list::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

.expert-item {
    padding: 6px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    font-size: 14px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.expert-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.expert-overlimit {
    background: #fef2f2;
    border-color: #fecaca;
    color: #dc2626;
}

.expert-count {
    font-size: 12px;
    opacity: 0.7;
    margin-left: 4px;
}

.result-section {
    border-top: 1px solid #e5e7eb;
    padding-top: 24px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.result-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.result-display {
    padding: 24px;
    background: linear-gradient(135deg, #42a5f5 0%, #64b5f6 100%);
    color: white;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 500;
    text-align: center;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6px 20px rgba(66, 165, 245, 0.3);
    flex: 1;
    transition: all 0.3s ease;
}

.result-display.has-result {
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #81c784 100%);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
    animation: resultPulse 0.6s ease-in-out;
}

.result-content {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.2rem;
    font-weight: 600;
}

.result-text {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.waiting-text {
    display: flex;
    align-items: center;
    gap: 8px;
    opacity: 0.8;
    font-size: 1rem;
}

@keyframes resultPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 图标样式 */
.icon {
    font-size: 1.1rem;
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.4);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #e5e7eb;
    color: #374151;
}

.modal-body {
    padding: 24px;
}

.log-textarea {
    width: 100%;
    min-height: 300px;
    padding: 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    resize: vertical;
    transition: all 0.3s ease;
}

.log-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 页脚 */
.footer {
    text-align: center;
    padding: 16px;
    color: #374151;
    font-size: 14px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(66, 165, 245, 0.1);
}

.footer p {
    margin: 0;
    font-weight: 400;
    letter-spacing: 0.5px;
}

/* 响应式设计 */
@media (max-width: 1240px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .action-buttons {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .random-picker {
        padding: 16px;
        gap: 16px;
    }
    
    .card {
        padding: 20px;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .setting-label {
        min-width: auto;
    }
    
    .defender-list {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 8px;
    }
    
    .expert-list {
        max-height: 100px;
    }
}

@media (max-width: 480px) {
    .action-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .action-btn {
        width: 100%;
        justify-content: center;
    }
    
    .defender-list {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
}
</style>
