<h1>随机抽取系统软件设计说明书</h1>
<h2>1. 引言</h2>
<h3>1.1 编写目的</h3>
<p>本文档旨在详细描述随机抽取系统的软件设计，为软件著作权申请提供技术支撑，同时为后续的开发、测试和维护工作提供指导。本系统采用现代化的前端技术栈，实现了高效、公平、易用的评委随机抽取功能。</p>
<h3>1.2 项目背景</h3>
<p>随机抽取系统是一款专门用于答辩评委随机分配的桌面应用程序，解决了传统人工分配评委效率低、公平性难以保证、操作繁琐等问题。系统通过智能算法确保抽取的随机性和公平性，同时提供完整的操作审计功能。</p>
<h3>1.3 定义和缩略语</h3>
<ul>
<li><strong>Vue.js</strong>: 渐进式 JavaScript 框架，用于构建用户界面</li>
<li><strong>Electron</strong>: 跨平台桌面应用开发框架，基于 Chromium 和 Node.js</li>
<li><strong>SPA</strong>: 单页面应用程序(Single Page Application)</li>
<li><strong>Fisher-Yates</strong>: 洗牌算法，用于生成随机排列</li>
<li><strong>UTF-8</strong>: Unicode 转换格式，8 位编码</li>
<li><strong>DOM</strong>: 文档对象模型(Document Object Model)</li>
</ul>
<h3>1.4 参考资料</h3>
<ul>
<li>Vue.js 官方文档 v3.2.13</li>
<li>Electron 官方文档 v13.0.0</li>
<li>Element Plus 组件库文档 v2.10.4</li>
<li>JavaScript ES6+语言规范</li>
</ul>
<h2>2. 系统概述</h2>
<h3>2.1 系统目标</h3>
<p>开发一个高效、公平、易用的评委随机抽取系统，主要目标包括：</p>
<ul>
<li><strong>数据管理</strong>: 支持答辩人员与评委的关联管理，灵活的数据导入</li>
<li><strong>智能抽取</strong>: 实现基于 Fisher-Yates 算法的随机抽取机制</li>
<li><strong>公平控制</strong>: 通过次数限制和统计确保抽取的公平性</li>
<li><strong>操作审计</strong>: 完整的操作日志记录，支持追溯和审计</li>
<li><strong>用户体验</strong>: 直观友好的图形界面，简化操作流程</li>
</ul>
<h3>2.2 系统特点</h3>
<ul>
<li><strong>跨平台支持</strong>: 基于 Electron 框架，支持 Windows、macOS、Linux</li>
<li><strong>响应式设计</strong>: 适配不同屏幕尺寸，提供一致的用户体验</li>
<li><strong>本地化存储</strong>: 数据本地存储，保证数据安全性</li>
<li><strong>实时反馈</strong>: 操作结果实时显示，提供即时的视觉反馈</li>
<li><strong>可扩展性</strong>: 模块化设计，便于功能扩展和维护</li>
</ul>
<h3>2.3 系统功能概览</h3>
<div class="language-mermaid">mindmap
  root((随机抽取系统))
    数据管理
      文件上传
      数据解析
      格式验证
      存储管理
    随机抽取
      算法实现
      公平控制
      结果生成
      状态更新
    界面交互
      系统设置
      操作面板
      结果展示
      错误提示
    日志管理
      操作记录
      审计追溯
      日志导出
      历史查询
</div>
<h2>3. 系统架构设计</h2>
<h3>3.1 总体架构</h3>
<p>系统采用基于 Electron 的桌面应用架构，结合 Vue.js 前端框架，实现了主进程和渲染进程的分离设计：</p>
<div class="language-mermaid">graph TB
    subgraph &quot;Electron应用架构&quot;
        A[主进程 Main Process] --&gt; B[渲染进程 Renderer Process]
        B --&gt; C[Vue.js应用]

        subgraph &quot;Vue.js层&quot;
            C --&gt; D[组件层 Components]
            C --&gt; E[数据层 Data Layer]
            C --&gt; F[服务层 Services]
        end

        subgraph &quot;数据存储层&quot;
            G[本地文件系统]
            H[内存缓存]
        end

        E --&gt; G
        E --&gt; H
    end

    subgraph &quot;外部接口&quot;
        I[文件输入接口]
        J[用户界面接口]
    end

    I --&gt; F
    D --&gt; J
</div>
<h3>3.2 技术栈详述</h3>
<h4>3.2.1 前端技术栈</h4>
<ul>
<li><strong>Vue.js 3.2.13</strong>: 采用 Composition API，提供更好的类型推导和代码组织</li>
<li><strong>Element Plus 2.10.4</strong>: 基于 Vue 3 的组件库，提供丰富的 UI 组件</li>
<li><strong>JavaScript ES6+</strong>: 使用现代 JavaScript 特性，提高代码质量</li>
</ul>
<h4>3.2.2 桌面应用框架</h4>
<ul>
<li><strong>Electron 13.0.0</strong>: 跨平台桌面应用开发框架</li>
<li><strong>Node.js</strong>: 提供文件系统访问和系统级 API</li>
</ul>
<h4>3.2.3 构建工具链</h4>
<ul>
<li><strong>Vue CLI 5.0.0</strong>: Vue.js 官方脚手架工具</li>
<li><strong>Webpack</strong>: 模块打包工具</li>
<li><strong>Babel</strong>: JavaScript 编译器，支持 ES6+语法</li>
</ul>
<h3>3.3 系统分层架构</h3>
<div class="language-mermaid">graph TD
    subgraph &quot;表现层 Presentation Layer&quot;
        A1[用户界面组件]
        A2[交互控制器]
        A3[视图渲染器]
    end

    subgraph &quot;业务逻辑层 Business Logic Layer&quot;
        B1[数据管理服务]
        B2[随机抽取服务]
        B3[日志管理服务]
        B4[配置管理服务]
    end

    subgraph &quot;数据访问层 Data Access Layer&quot;
        C1[文件读写接口]
        C2[内存数据管理]
        C3[数据验证器]
    end

    subgraph &quot;基础设施层 Infrastructure Layer&quot;
        D1[Electron主进程]
        D2[文件系统]
        D3[系统API]
    end

    A1 --&gt; B1
    A2 --&gt; B2
    A3 --&gt; B3
    B1 --&gt; C1
    B2 --&gt; C2
    B3 --&gt; C3
    C1 --&gt; D2
    C2 --&gt; D1
    C3 --&gt; D3
</div>
<h2>4. 功能模块设计</h2>
<h3>4.1 数据管理模块</h3>
<h4>4.1.1 模块职责</h4>
<p>数据管理模块负责答辩人员和评委数据的完整生命周期管理，包括数据的导入、解析、验证、存储和检索。</p>
<h4>4.1.2 核心功能</h4>
<ul>
<li><strong>文件上传处理</strong>: 支持 UTF-8 编码的文本文件导入</li>
<li><strong>数据解析引擎</strong>: 智能解析多种分隔符格式</li>
<li><strong>数据结构化</strong>: 将原始数据转换为系统内部数据结构</li>
<li><strong>数据验证</strong>: 确保数据完整性和格式正确性</li>
<li><strong>评委全集构建</strong>: 自动提取并构建评委候选池</li>
</ul>
<h4>4.1.3 数据处理流程</h4>
<div class="language-mermaid">flowchart TD
    A[用户选择文件] --&gt; B{文件格式检查}
    B --&gt;|通过| C[读取文件内容]
    B --&gt;|失败| D[显示错误信息]
    C --&gt; E[UTF-8编码解析]
    E --&gt; F[按行分割数据]
    F --&gt; G[解析每行数据]
    G --&gt; H{数据格式验证}
    H --&gt;|通过| I[构建答辩人数据结构]
    H --&gt;|失败| J[记录错误行]
    I --&gt; K[提取评委全集]
    K --&gt; L[初始化评委计数]
    L --&gt; M[更新界面显示]
    J --&gt; N[显示解析报告]
    M --&gt; O[数据加载完成]
    N --&gt; O
</div>
<h4>4.1.4 核心算法实现</h4>
<pre><code class="language-javascript">// 数据解析核心算法
handleMapFileChange(event) {
    const file = event.target.files[0];
    if (!file) {
        this.showError('请选择有效的文件');
        return;
    }

    const reader = new FileReader();
    reader.onload = () =&gt; {
        try {
            const content = reader.result;
            const parseResult = this.parseFileContent(content);

            if (parseResult.success) {
                this.updateDataStructures(parseResult.data);
                this.showSuccess(`成功加载 ${parseResult.defenderCount} 个答辩人的数据`);
            } else {
                this.showError(`解析失败: ${parseResult.error}`);
            }
        } catch (error) {
            this.handleParseError(error);
        }
    };

    reader.readAsText(file, 'UTF-8');
}

// 文件内容解析算法
parseFileContent(content) {
    const lines = content.split(/\r?\n/);
    const allExperts = new Set();
    const newDefenderData = {};
    const errors = [];

    lines.forEach((line, index) =&gt; {
        const trimmedLine = line.trim();
        if (trimmedLine) {
            const parseLineResult = this.parseLine(trimmedLine, index + 1);
            if (parseLineResult.success) {
                const { defenderName, experts } = parseLineResult.data;
                newDefenderData[defenderName] = experts;
                experts.forEach(expert =&gt; allExperts.add(expert));
            } else {
                errors.push(parseLineResult.error);
            }
        }
    });

    return {
        success: errors.length === 0,
        data: {
            defenderData: newDefenderData,
            expertSet: allExperts
        },
        errors: errors,
        defenderCount: Object.keys(newDefenderData).length
    };
}
</code></pre>
<h3>4.2 随机抽取模块</h3>
<h4>4.2.1 模块职责</h4>
<p>随机抽取模块是系统的核心，负责实现公平、随机的评委抽取算法，确保每次抽取结果的随机性和整体分配的公平性。</p>
<h4>4.2.2 抽取策略设计</h4>
<ul>
<li><strong>Fisher-Yates 洗牌算法</strong>: 保证数学意义上的真随机性</li>
<li><strong>次数限制机制</strong>: 确保每个评委的工作负载均衡</li>
<li><strong>动态候选池</strong>: 实时更新可用评委列表</li>
<li><strong>智能筛选</strong>: 优先选择抽取次数较少的评委</li>
</ul>
<h4>4.2.3 抽取流程设计</h4>
<div class="language-mermaid">flowchart TD
    A[用户点击答辩人] --&gt; B[获取候选评委列表]
    B --&gt; C{候选评委是否为空}
    C --&gt;|是| D[显示无可用评委]
    C --&gt;|否| E[应用次数限制筛选]
    E --&gt; F[获取可用评委池]
    F --&gt; G{可用评委数量检查}
    G --&gt;|不足| H[放宽限制条件]
    G --&gt;|充足| I[执行Fisher-Yates洗牌]
    H --&gt; I
    I --&gt; J[选取前N个评委]
    J --&gt; K[更新评委计数]
    K --&gt; L[生成抽取结果]
    L --&gt; M[记录操作日志]
    M --&gt; N[更新界面显示]
    N --&gt; O[抽取完成]
</div>
<h4>4.2.4 Fisher-Yates 算法实现</h4>
<pre><code class="language-javascript">// Fisher-Yates洗牌算法实现
getRandomKeys(array, num) {
    if (!array || array.length === 0) {
        return [];
    }

    // 创建数组副本，避免修改原数组
    const shuffled = [...array];
    let currentIndex = shuffled.length;

    // 从后向前遍历，确保每个位置都有相等的概率
    while (currentIndex &gt; 0) {
        // 生成随机索引
        const randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex--;

        // 交换当前元素和随机选择的元素
        [shuffled[currentIndex], shuffled[randomIndex]] =
        [shuffled[randomIndex], shuffled[currentIndex]];
    }

    // 返回前num个元素
    return shuffled.slice(0, Math.min(num, shuffled.length));
}

// 智能抽取算法
performIntelligentSelection(defenderName) {
    const candidateExperts = this.defenderData[defenderName];
    if (!candidateExperts?.length) {
        throw new Error('该答辩人没有可用的评委');
    }

    // 第一轮筛选：未达到次数限制的评委
    let availableExperts = candidateExperts.filter(expert =&gt;
        this.expertDict[expert] &lt; this.num
    );

    // 如果可用评委不足，进行智能补充
    if (availableExperts.length &lt; 3) {
        availableExperts = this.expandAvailablePool(candidateExperts);
    }

    // 执行随机抽取
    const selectedExperts = this.getRandomKeys(availableExperts, 3);

    // 更新统计信息
    this.updateExpertStatistics(selectedExperts);

    return selectedExperts;
}
</code></pre>
<h3>4.3 界面交互模块</h3>
<h4>4.3.1 模块职责</h4>
<p>界面交互模块负责提供直观友好的用户界面，处理用户输入，展示系统状态和操作结果。</p>
<h4>4.3.2 界面组件架构</h4>
<div class="language-mermaid">graph TD
    subgraph &quot;主界面容器&quot;
        A[RandomPicker.vue]
    end

    subgraph &quot;头部区域&quot;
        B[HeaderCard 头部卡片]
        B1[系统标题]
        B2[功能按钮组]
    end

    subgraph &quot;左侧面板&quot;
        C[SettingsCard 设置卡片]
        C1[次数限制设置]
        C2[文件上传组件]
        D[DefenderCard 答辩人卡片]
        D1[答辩人列表]
        D2[操作按钮]
    end

    subgraph &quot;右侧面板&quot;
        E[ResultCard 结果卡片]
        E1[评委列表显示]
        E2[抽取结果展示]
        E3[统计信息]
    end

    subgraph &quot;模态对话框&quot;
        F[LogModal 日志对话框]
        F1[日志内容显示]
        F2[操作控制]
    end

    A --&gt; B
    A --&gt; C
    A --&gt; D
    A --&gt; E
    A --&gt; F
    B --&gt; B1
    B --&gt; B2
    C --&gt; C1
    C --&gt; C2
    D --&gt; D1
    D --&gt; D2
    E --&gt; E1
    E --&gt; E2
    E --&gt; E3
    F --&gt; F1
    F --&gt; F2
</div>
<h4>4.3.3 交互流程设计</h4>
<div class="language-mermaid">sequenceDiagram
    participant U as 用户
    participant UI as 界面组件
    participant DM as 数据管理
    participant RA as 随机抽取
    participant LM as 日志管理

    U-&gt;&gt;UI: 上传文件
    UI-&gt;&gt;DM: 处理文件上传
    DM-&gt;&gt;DM: 解析文件内容
    DM-&gt;&gt;UI: 返回解析结果
    UI-&gt;&gt;U: 显示加载状态

    U-&gt;&gt;UI: 点击答辩人
    UI-&gt;&gt;RA: 执行随机抽取
    RA-&gt;&gt;RA: 筛选可用评委
    RA-&gt;&gt;RA: 执行洗牌算法
    RA-&gt;&gt;UI: 返回抽取结果
    RA-&gt;&gt;LM: 记录操作日志
    UI-&gt;&gt;U: 显示抽取结果

    U-&gt;&gt;UI: 查看日志
    UI-&gt;&gt;LM: 获取日志内容
    LM-&gt;&gt;UI: 返回日志数据
    UI-&gt;&gt;U: 显示日志对话框
</div>
<h3>4.4 日志管理模块</h3>
<h4>4.4.1 模块职责</h4>
<p>日志管理模块负责记录系统的所有关键操作，提供完整的审计追踪功能，支持操作历史的查询和分析。</p>
<h4>4.4.2 日志记录策略</h4>
<ul>
<li><strong>操作日志</strong>: 记录每次抽取操作的详细信息</li>
<li><strong>系统日志</strong>: 记录系统启动、配置变更等事件</li>
<li><strong>错误日志</strong>: 记录异常情况和错误信息</li>
<li><strong>性能日志</strong>: 记录关键操作的执行时间</li>
</ul>
<h4>4.4.3 日志格式设计</h4>
<pre><code class="language-javascript">// 标准日志格式
const logEntry = {
  timestamp: &quot;2024-01-15 14:30:25&quot;,
  operation: &quot;RANDOM_SELECTION&quot;,
  defender: &quot;张三&quot;,
  selectedExperts: [&quot;评委A&quot;, &quot;评委B&quot;, &quot;评委C&quot;],
  availableCount: 15,
  executionTime: &quot;2ms&quot;,
  result: &quot;SUCCESS&quot;,
};

// 日志文本格式
const logText = `${timestamp}, 答辩人:${defender}, 评委:${experts.join(
  &quot;、&quot;
)}, 可用评委数:${count}`;
</code></pre>
<h2>5. 数据结构设计</h2>
<h3>5.1 核心数据结构</h3>
<h4>5.1.1 答辩人数据结构</h4>
<pre><code class="language-javascript">// 答辩人与评委关联数据
defenderData: {
    &quot;张三&quot;: [&quot;王教授&quot;, &quot;李教授&quot;, &quot;赵教授&quot;, &quot;钱教授&quot;],
    &quot;李四&quot;: [&quot;王教授&quot;, &quot;孙教授&quot;, &quot;周教授&quot;, &quot;吴教授&quot;],
    &quot;王五&quot;: [&quot;李教授&quot;, &quot;赵教授&quot;, &quot;郑教授&quot;, &quot;王教授&quot;]
}
</code></pre>
<h4>5.1.2 评委统计数据结构</h4>
<pre><code class="language-javascript">// 评委抽取次数统计
expertDict: {
    &quot;王教授&quot;: 2,
    &quot;李教授&quot;: 1,
    &quot;赵教授&quot;: 3,
    &quot;钱教授&quot;: 0,
    &quot;孙教授&quot;: 1
}
</code></pre>
<h4>5.1.3 系统配置数据结构</h4>
<pre><code class="language-javascript">// 系统配置参数
systemConfig: {
    maxSelectionCount: 4,        // 最大抽取次数
    expertsPerSelection: 3,      // 每次抽取评委数量
    enableFairMode: true,        // 启用公平模式
    logRetentionDays: 30,        // 日志保留天数
    autoSave: true               // 自动保存配置
}
</code></pre>
<h3>5.2 数据流转图</h3>
<div class="language-mermaid">graph LR
    subgraph &quot;数据输入&quot;
        A[文本文件] --&gt; B[文件解析器]
    end

    subgraph &quot;数据处理&quot;
        B --&gt; C[数据验证器]
        C --&gt; D[数据结构化]
        D --&gt; E[内存存储]
    end

    subgraph &quot;业务逻辑&quot;
        E --&gt; F[随机抽取引擎]
        F --&gt; G[公平性控制器]
        G --&gt; H[结果生成器]
    end

    subgraph &quot;数据输出&quot;
        H --&gt; I[界面显示]
        H --&gt; J[日志记录]
        J --&gt; K[持久化存储]
    end
</div>
<h3>5.3 文件格式规范</h3>
<h4>5.3.1 输入文件格式</h4>
<pre><code># 标准格式（中文逗号分隔）
答辩人1，评委A，评委B，评委C，评委D
答辩人2，评委B，评委C，评委E，评委F

# 支持的替代格式
答辩人1,评委A,评委B,评委C,评委D    # 英文逗号
答辩人1	评委A	评委B	评委C	评委D    # Tab分隔
答辩人1 评委A 评委B 评委C 评委D      # 空格分隔
</code></pre>
<h4>5.3.2 数据验证规则</h4>
<ul>
<li>每行至少包含 2 个字段（答辩人+至少 1 个评委）</li>
<li>答辩人姓名不能为空且不能重复</li>
<li>评委姓名不能为空</li>
<li>支持 UTF-8 编码，兼容中文字符</li>
<li>自动过滤空行和无效行</li>
</ul>
<h2>6. 算法设计</h2>
<h3>6.1 Fisher-Yates 洗牌算法详述</h3>
<h4>6.1.1 算法原理</h4>
<p>Fisher-Yates 洗牌算法是一种用于生成有限序列随机排列的算法，能够保证每种排列出现的概率相等。</p>
<h4>6.1.2 算法复杂度分析</h4>
<ul>
<li><strong>时间复杂度</strong>: O(n)，其中 n 为数组长度</li>
<li><strong>空间复杂度</strong>: O(n)，需要创建数组副本</li>
<li><strong>随机性</strong>: 数学证明的均匀分布</li>
</ul>
<h4>6.1.3 算法流程图</h4>
<div class="language-mermaid">flowchart TD
    A[输入数组 array] --&gt; B[创建副本 shuffled]
    B --&gt; C[设置索引 i = length-1]
    C --&gt; D{i &gt; 0?}
    D --&gt;|否| E[返回前num个元素]
    D --&gt;|是| F[生成随机数 j = random(0, i)]
    F --&gt; G[交换 shuffled[i] 和 shuffled[j]]
    G --&gt; H[i = i - 1]
    H --&gt; D
    E --&gt; I[算法结束]
</div>
<h4>6.1.4 算法优化策略</h4>
<pre><code class="language-javascript">// 优化版Fisher-Yates算法
getOptimizedRandomKeys(array, num) {
    if (!array?.length || num &lt;= 0) return [];

    const n = array.length;
    const k = Math.min(num, n);

    // 如果需要的数量接近总数，使用完整洗牌
    if (k &gt; n * 0.7) {
        return this.fullShuffle(array).slice(0, k);
    }

    // 否则使用部分洗牌优化
    return this.partialShuffle(array, k);
}

// 部分洗牌优化（只洗牌前k个位置）
partialShuffle(array, k) {
    const result = [...array];
    for (let i = 0; i &lt; k; i++) {
        const j = i + Math.floor(Math.random() * (result.length - i));
        [result[i], result[j]] = [result[j], result[i]];
    }
    return result.slice(0, k);
}
</code></pre>
<h3>6.2 公平性保证算法</h3>
<h4>6.2.1 动态权重分配</h4>
<pre><code class="language-javascript">// 基于抽取次数的权重计算
calculateExpertWeights(candidateExperts) {
    const weights = {};
    const maxCount = Math.max(...candidateExperts.map(e =&gt; this.expertDict[e]));

    candidateExperts.forEach(expert =&gt; {
        const count = this.expertDict[expert];
        // 次数越少，权重越高
        weights[expert] = maxCount - count + 1;
    });

    return weights;
}

// 加权随机选择
weightedRandomSelection(candidates, weights, num) {
    const selected = [];
    const available = [...candidates];

    for (let i = 0; i &lt; num &amp;&amp; available.length &gt; 0; i++) {
        const totalWeight = available.reduce((sum, expert) =&gt;
            sum + weights[expert], 0);

        let random = Math.random() * totalWeight;
        let selectedIndex = 0;

        for (let j = 0; j &lt; available.length; j++) {
            random -= weights[available[j]];
            if (random &lt;= 0) {
                selectedIndex = j;
                break;
            }
        }

        selected.push(available[selectedIndex]);
        available.splice(selectedIndex, 1);
    }

    return selected;
}
</code></pre>
<h3>6.3 性能优化算法</h3>
<h4>6.3.1 缓存策略</h4>
<pre><code class="language-javascript">// LRU缓存实现
class LRUCache {
  constructor(capacity) {
    this.capacity = capacity;
    this.cache = new Map();
  }

  get(key) {
    if (this.cache.has(key)) {
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return null;
  }

  set(key, value) {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size &gt;= this.capacity) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }
}
</code></pre>
<h2>7. 界面设计</h2>
<h3>7.1 界面架构设计</h3>
<h4>7.1.1 响应式布局策略</h4>
<div class="language-mermaid">graph TD
    subgraph &quot;桌面端布局 (&gt;1200px)&quot;
        A1[头部区域 100%]
        A2[左侧面板 40%]
        A3[右侧面板 60%]
    end

    subgraph &quot;平板端布局 (768px-1200px)&quot;
        B1[头部区域 100%]
        B2[左侧面板 100%]
        B3[右侧面板 100%]
    end

    subgraph &quot;移动端布局 (&lt;768px)&quot;
        C1[头部区域 100%]
        C2[折叠式面板 100%]
        C3[底部操作区 100%]
    end
</div>
<h4>7.1.2 组件层次结构</h4>
<div class="language-mermaid">graph TD
    A[App.vue 根组件] --&gt; B[RandomPicker.vue 主组件]

    B --&gt; C[HeaderSection 头部区域]
    B --&gt; D[MainContent 主内容区]
    B --&gt; E[FooterSection 页脚区域]
    B --&gt; F[ModalContainer 模态框容器]

    C --&gt; C1[TitleBar 标题栏]
    C --&gt; C2[ActionButtons 操作按钮组]

    D --&gt; D1[LeftPanel 左侧面板]
    D --&gt; D2[RightPanel 右侧面板]

    D1 --&gt; D11[SettingsCard 设置卡片]
    D1 --&gt; D12[DefenderList 答辩人列表]

    D2 --&gt; D21[ExpertGrid 评委网格]
    D2 --&gt; D22[ResultDisplay 结果显示]

    F --&gt; F1[LogModal 日志模态框]
    F --&gt; F2[ConfirmModal 确认模态框]
</div>
<h3>7.2 用户体验设计</h3>
<h4>7.2.1 交互状态图</h4>
<div class="language-mermaid">stateDiagram-v2
    [*] --&gt; 初始状态
    初始状态 --&gt; 数据加载中: 用户上传文件
    数据加载中 --&gt; 数据就绪: 加载成功
    数据加载中 --&gt; 错误状态: 加载失败
    数据就绪 --&gt; 抽取中: 用户点击抽取
    抽取中 --&gt; 结果显示: 抽取完成
    结果显示 --&gt; 数据就绪: 继续操作
    错误状态 --&gt; 初始状态: 重新开始
    数据就绪 --&gt; 日志查看: 查看日志
    日志查看 --&gt; 数据就绪: 关闭日志
</div>
<h4>7.2.2 视觉反馈系统</h4>
<pre><code class="language-javascript">// 状态反馈配置
const feedbackConfig = {
  loading: {
    spinner: true,
    message: &quot;正在处理...&quot;,
    duration: 0,
  },
  success: {
    icon: &quot;✅&quot;,
    message: &quot;操作成功&quot;,
    duration: 3000,
    animation: &quot;bounce&quot;,
  },
  error: {
    icon: &quot;❌&quot;,
    message: &quot;操作失败&quot;,
    duration: 5000,
    animation: &quot;shake&quot;,
  },
  warning: {
    icon: &quot;⚠️&quot;,
    message: &quot;请注意&quot;,
    duration: 4000,
    animation: &quot;pulse&quot;,
  },
};
</code></pre>
<h3>7.3 主题和样式系统</h3>
<h4>7.3.1 设计令牌系统</h4>
<pre><code class="language-css">:root {
  /* 主色调 */
  --primary-color: #42a5f5;
  --primary-light: #90caf9;
  --primary-dark: #1976d2;

  /* 功能色彩 */
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --info-color: #2196f3;

  /* 中性色彩 */
  --text-primary: #212121;
  --text-secondary: #757575;
  --background-primary: #ffffff;
  --background-secondary: #f5f5f5;

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 圆角系统 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;

  /* 阴影系统 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
}
</code></pre>
<h2>8. 安全性设计</h2>
<h3>8.1 数据安全策略</h3>
<h4>8.1.1 输入验证机制</h4>
<pre><code class="language-javascript">// 输入数据验证器
class InputValidator {
  static validateFile(file) {
    const validations = [
      this.checkFileType(file),
      this.checkFileSize(file),
      this.checkFileName(file),
    ];

    return validations.every((v) =&gt; v.isValid);
  }

  static checkFileType(file) {
    const allowedTypes = [&quot;text/plain&quot;, &quot;text/csv&quot;];
    return {
      isValid: allowedTypes.includes(file.type),
      message: &quot;仅支持文本文件格式&quot;,
    };
  }

  static checkFileSize(file) {
    const maxSize = 10 * 1024 * 1024; // 10MB
    return {
      isValid: file.size &lt;= maxSize,
      message: &quot;文件大小不能超过10MB&quot;,
    };
  }

  static sanitizeInput(input) {
    return input
      .replace(/[&lt;&gt;]/g, &quot;&quot;) // 移除HTML标签
      .replace(/javascript:/gi, &quot;&quot;) // 移除JavaScript协议
      .trim();
  }
}
</code></pre>
<h4>8.1.2 数据加密存储</h4>
<pre><code class="language-javascript">// 敏感数据加密
class DataEncryption {
  static encrypt(data, key) {
    // 使用AES加密算法
    const cipher = crypto.createCipher(&quot;aes-256-cbc&quot;, key);
    let encrypted = cipher.update(JSON.stringify(data), &quot;utf8&quot;, &quot;hex&quot;);
    encrypted += cipher.final(&quot;hex&quot;);
    return encrypted;
  }

  static decrypt(encryptedData, key) {
    const decipher = crypto.createDecipher(&quot;aes-256-cbc&quot;, key);
    let decrypted = decipher.update(encryptedData, &quot;hex&quot;, &quot;utf8&quot;);
    decrypted += decipher.final(&quot;utf8&quot;);
    return JSON.parse(decrypted);
  }
}
</code></pre>
<h3>8.2 系统安全架构</h3>
<div class="language-mermaid">graph TD
    subgraph &quot;安全防护层&quot;
        A[输入验证] --&gt; B[数据清理]
        B --&gt; C[权限检查]
        C --&gt; D[加密存储]
    end

    subgraph &quot;业务逻辑层&quot;
        E[数据处理]
        F[算法执行]
        G[结果生成]
    end

    subgraph &quot;审计日志层&quot;
        H[操作记录]
        I[异常监控]
        J[性能统计]
    end

    D --&gt; E
    E --&gt; F
    F --&gt; G
    G --&gt; H
    H --&gt; I
    I --&gt; J
</div>
<h2>9. 性能优化</h2>
<h3>9.1 前端性能优化</h3>
<h4>9.1.1 虚拟滚动实现</h4>
<pre><code class="language-javascript">// 虚拟滚动组件
class VirtualScroll {
  constructor(container, itemHeight, visibleCount) {
    this.container = container;
    this.itemHeight = itemHeight;
    this.visibleCount = visibleCount;
    this.scrollTop = 0;
    this.totalHeight = 0;
  }

  updateVisibleItems(allItems) {
    const startIndex = Math.floor(this.scrollTop / this.itemHeight);
    const endIndex = Math.min(startIndex + this.visibleCount, allItems.length);

    return {
      visibleItems: allItems.slice(startIndex, endIndex),
      offsetY: startIndex * this.itemHeight,
      totalHeight: allItems.length * this.itemHeight,
    };
  }
}
</code></pre>
<h4>9.1.2 防抖和节流优化</h4>
<pre><code class="language-javascript">// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () =&gt; {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 节流函数
function throttle(func, limit) {
  let inThrottle;
  return function (...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() =&gt; (inThrottle = false), limit);
    }
  };
}
</code></pre>
<h3>9.2 内存管理优化</h3>
<h4>9.2.1 对象池模式</h4>
<pre><code class="language-javascript">// 对象池实现
class ObjectPool {
  constructor(createFn, resetFn, initialSize = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    this.pool = [];

    // 预创建对象
    for (let i = 0; i &lt; initialSize; i++) {
      this.pool.push(this.createFn());
    }
  }

  acquire() {
    return this.pool.length &gt; 0 ? this.pool.pop() : this.createFn();
  }

  release(obj) {
    this.resetFn(obj);
    this.pool.push(obj);
  }
}
</code></pre>
<h3>9.3 性能监控系统</h3>
<div class="language-mermaid">graph LR
    subgraph &quot;性能指标收集&quot;
        A[响应时间] --&gt; D[性能分析器]
        B[内存使用] --&gt; D
        C[CPU占用] --&gt; D
    end

    subgraph &quot;性能分析&quot;
        D --&gt; E[瓶颈识别]
        E --&gt; F[优化建议]
        F --&gt; G[性能报告]
    end

    subgraph &quot;优化执行&quot;
        G --&gt; H[代码优化]
        H --&gt; I[算法改进]
        I --&gt; J[资源优化]
    end
</div>
<h2>10. 部署和维护</h2>
<h3>10.1 构建配置详述</h3>
<h4>10.1.1 Webpack 配置优化</h4>
<pre><code class="language-javascript">// vue.config.js
const { defineConfig } = require(&quot;@vue/cli-service&quot;);

module.exports = defineConfig({
  transpileDependencies: true,

  // 生产环境优化
  configureWebpack: (config) =&gt; {
    if (process.env.NODE_ENV === &quot;production&quot;) {
      // 代码分割
      config.optimization.splitChunks = {
        chunks: &quot;all&quot;,
        cacheGroups: {
          vendor: {
            name: &quot;chunk-vendors&quot;,
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: &quot;initial&quot;,
          },
          common: {
            name: &quot;chunk-common&quot;,
            minChunks: 2,
            priority: 5,
            chunks: &quot;initial&quot;,
          },
        },
      };
    }
  },

  // Electron配置
  pluginOptions: {
    electronBuilder: {
      nodeIntegration: true,
      builderOptions: {
        appId: &quot;com.example.random-picker&quot;,
        productName: &quot;随机抽取系统&quot;,
        directories: {
          output: &quot;dist_electron&quot;,
        },
        files: [&quot;dist_electron/**/*&quot;],
        win: {
          icon: &quot;build/icon.ico&quot;,
          target: &quot;nsis&quot;,
        },
        mac: {
          icon: &quot;build/icon.icns&quot;,
          target: &quot;dmg&quot;,
        },
        linux: {
          icon: &quot;build/icon.png&quot;,
          target: &quot;AppImage&quot;,
        },
      },
    },
  },
});
</code></pre>
<h4>10.1.2 构建流程图</h4>
<div class="language-mermaid">flowchart TD
    A[源代码] --&gt; B[ESLint检查]
    B --&gt; C[TypeScript编译]
    C --&gt; D[Vue组件编译]
    D --&gt; E[Webpack打包]
    E --&gt; F[代码压缩]
    F --&gt; G[资源优化]
    G --&gt; H[Electron打包]
    H --&gt; I[平台适配]
    I --&gt; J[安装包生成]

    subgraph &quot;质量检查&quot;
        K[单元测试]
        L[集成测试]
        M[性能测试]
    end

    J --&gt; K
    K --&gt; L
    L --&gt; M
    M --&gt; N[发布准备]
</div>
<h3>10.2 版本管理策略</h3>
<h4>10.2.1 语义化版本控制</h4>
<pre><code class="language-json">{
  &quot;version&quot;: &quot;1.2.3&quot;,
  &quot;versionComponents&quot;: {
    &quot;major&quot;: 1, // 重大版本更新
    &quot;minor&quot;: 2, // 功能性更新
    &quot;patch&quot;: 3 // 错误修复
  },
  &quot;releaseNotes&quot;: {
    &quot;1.2.3&quot;: [
      &quot;修复了文件解析的编码问题&quot;,
      &quot;优化了随机算法的性能&quot;,
      &quot;改进了用户界面的响应速度&quot;
    ]
  }
}
</code></pre>
<h4>10.2.2 发布流程</h4>
<div class="language-mermaid">gitgraph
    commit id: &quot;开发开始&quot;
    branch develop
    checkout develop
    commit id: &quot;功能开发&quot;
    commit id: &quot;单元测试&quot;
    branch feature/new-algorithm
    checkout feature/new-algorithm
    commit id: &quot;算法优化&quot;
    checkout develop
    merge feature/new-algorithm
    commit id: &quot;集成测试&quot;
    checkout main
    merge develop
    commit id: &quot;v1.2.3发布&quot;
    tag: &quot;v1.2.3&quot;
</div>
<h3>10.3 维护和监控</h3>
<h4>10.3.1 错误监控系统</h4>
<pre><code class="language-javascript">// 全局错误处理
class ErrorMonitor {
  static init() {
    // 捕获未处理的Promise拒绝
    window.addEventListener(
      &quot;unhandledrejection&quot;,
      this.handleUnhandledRejection
    );

    // 捕获全局错误
    window.addEventListener(&quot;error&quot;, this.handleGlobalError);

    // Vue错误处理
    app.config.errorHandler = this.handleVueError;
  }

  static handleUnhandledRejection(event) {
    console.error(&quot;未处理的Promise拒绝:&quot;, event.reason);
    this.reportError({
      type: &quot;unhandledRejection&quot;,
      error: event.reason,
      timestamp: new Date().toISOString(),
    });
  }

  static handleGlobalError(event) {
    console.error(&quot;全局错误:&quot;, event.error);
    this.reportError({
      type: &quot;globalError&quot;,
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error,
      timestamp: new Date().toISOString(),
    });
  }

  static reportError(errorInfo) {
    // 发送错误报告到监控系统
    // 这里可以集成第三方监控服务
    localStorage.setItem(&quot;lastError&quot;, JSON.stringify(errorInfo));
  }
}
</code></pre>
<h2>11. 创新点和技术亮点</h2>
<h3>11.1 核心创新点</h3>
<h4>11.1.1 智能公平性算法</h4>
<p>本系统创新性地结合了随机性和公平性，通过动态权重分配确保评委工作负载的均衡分布：</p>
<pre><code class="language-javascript">// 创新的公平性权重算法
calculateFairnessWeight(expert, globalStats) {
    const currentCount = this.expertDict[expert];
    const averageCount = globalStats.totalSelections / globalStats.totalExperts;
    const deviation = currentCount - averageCount;

    // 使用指数衰减函数计算权重
    const weight = Math.exp(-deviation * this.fairnessCoefficient);
    return Math.max(weight, this.minWeight);
}
</code></pre>
<h4>11.1.2 自适应候选池管理</h4>
<p>系统能够根据实际情况自动调整候选评委池，确保在各种约束条件下都能产生有效的抽取结果：</p>
<div class="language-mermaid">flowchart TD
    A[候选评委池] --&gt; B{是否满足最小数量}
    B --&gt;|是| C[标准抽取流程]
    B --&gt;|否| D[启动自适应扩展]
    D --&gt; E[放宽次数限制]
    E --&gt; F[重新评估候选池]
    F --&gt; G{扩展后是否充足}
    G --&gt;|是| C
    G --&gt;|否| H[智能补充策略]
    H --&gt; I[跨组评委借调]
    I --&gt; C
</div>
<h3>11.2 技术亮点</h3>
<h4>11.2.1 响应式数据流架构</h4>
<p>采用 Vue 3 的 Composition API 实现了高效的响应式数据管理：</p>
<pre><code class="language-javascript">// 响应式数据流管理
import { reactive, computed, watch } from &quot;vue&quot;;

export function useDataFlow() {
  const state = reactive({
    defenderData: {},
    expertDict: {},
    selectionHistory: [],
  });

  // 计算属性：可用评委统计
  const availableExperts = computed(() =&gt; {
    return Object.entries(state.expertDict)
      .filter(([_, count]) =&gt; count &lt; maxSelectionCount.value)
      .map(([name, _]) =&gt; name);
  });

  // 监听数据变化，自动更新统计
  watch(
    () =&gt; state.expertDict,
    (newDict) =&gt; {
      updateGlobalStatistics(newDict);
    },
    { deep: true }
  );

  return {
    state,
    availableExperts,
    updateExpertCount,
    resetStatistics,
  };
}
</code></pre>
<h4>11.2.2 高性能渲染优化</h4>
<p>实现了基于虚拟滚动的大数据量渲染优化：</p>
<pre><code class="language-javascript">// 虚拟滚动优化实现
export function useVirtualScroll(itemHeight = 50, visibleCount = 10) {
  const scrollContainer = ref(null);
  const scrollTop = ref(0);

  const visibleItems = computed(() =&gt; {
    const start = Math.floor(scrollTop.value / itemHeight);
    const end = Math.min(start + visibleCount, totalItems.value);

    return {
      items: allItems.value.slice(start, end),
      offsetY: start * itemHeight,
      totalHeight: totalItems.value * itemHeight,
    };
  });

  const handleScroll = throttle((event) =&gt; {
    scrollTop.value = event.target.scrollTop;
  }, 16); // 60fps

  return {
    scrollContainer,
    visibleItems,
    handleScroll,
  };
}
</code></pre>
<h3>11.3 用户体验创新</h3>
<h4>11.3.1 智能操作提示系统</h4>
<pre><code class="language-javascript">// 智能提示系统
class SmartHintSystem {
  constructor() {
    this.hintRules = [
      {
        condition: () =&gt; this.isFirstTimeUser(),
        hint: &quot;欢迎使用！请先上传答辩人名单文件&quot;,
        type: &quot;welcome&quot;,
      },
      {
        condition: () =&gt; this.hasUnbalancedExperts(),
        hint: &quot;检测到评委工作负载不均，建议启用公平模式&quot;,
        type: &quot;suggestion&quot;,
      },
      {
        condition: () =&gt; this.isNearSelectionLimit(),
        hint: &quot;部分评委接近抽取次数上限，请注意调整&quot;,
        type: &quot;warning&quot;,
      },
    ];
  }

  getActiveHints() {
    return this.hintRules
      .filter((rule) =&gt; rule.condition())
      .map((rule) =&gt; ({
        message: rule.hint,
        type: rule.type,
        timestamp: Date.now(),
      }));
  }
}
</code></pre>
<h2>12. 技术难点及解决方案</h2>
<h3>12.1 随机性与公平性平衡</h3>
<h4>12.1.1 问题描述</h4>
<p>在保证抽取结果随机性的同时，需要确保长期使用中评委工作负载的公平分配，这是一个典型的多目标优化问题。</p>
<h4>12.1.2 解决方案</h4>
<p>采用加权随机算法，结合动态权重调整机制：</p>
<pre><code class="language-javascript">// 多目标优化算法
class MultiObjectiveOptimizer {
  constructor(randomnessWeight = 0.7, fairnessWeight = 0.3) {
    this.randomnessWeight = randomnessWeight;
    this.fairnessWeight = fairnessWeight;
  }

  optimizedSelection(candidates, targetCount) {
    // 计算随机性得分
    const randomnessScores = this.calculateRandomnessScores(candidates);

    // 计算公平性得分
    const fairnessScores = this.calculateFairnessScores(candidates);

    // 综合评分
    const compositeScores = candidates.map((candidate) =&gt; ({
      name: candidate,
      score:
        this.randomnessWeight * randomnessScores[candidate] +
        this.fairnessWeight * fairnessScores[candidate],
    }));

    // 基于综合评分的概率选择
    return this.probabilisticSelection(compositeScores, targetCount);
  }
}
</code></pre>
<h3>12.2 大数据量处理优化</h3>
<h4>12.2.1 问题描述</h4>
<p>当答辩人数量和评委数量较大时，系统需要处理大量的数据计算和界面渲染，可能导致性能问题。</p>
<h4>12.2.2 解决方案架构</h4>
<div class="language-mermaid">graph TD
    subgraph &quot;数据处理层&quot;
        A[数据分片处理] --&gt; B[并行计算]
        B --&gt; C[结果合并]
    end

    subgraph &quot;缓存层&quot;
        D[计算结果缓存] --&gt; E[LRU淘汰策略]
        E --&gt; F[内存管理]
    end

    subgraph &quot;渲染优化层&quot;
        G[虚拟滚动] --&gt; H[懒加载]
        H --&gt; I[增量更新]
    end

    C --&gt; D
    F --&gt; G
</div>
<h4>12.2.3 具体实现</h4>
<pre><code class="language-javascript">// 大数据量处理优化
class BigDataProcessor {
  constructor(chunkSize = 1000) {
    this.chunkSize = chunkSize;
    this.cache = new LRUCache(100);
    this.worker = new Worker(&quot;dataProcessor.worker.js&quot;);
  }

  async processLargeDataset(data) {
    const chunks = this.chunkData(data, this.chunkSize);
    const promises = chunks.map((chunk) =&gt; this.processChunkInWorker(chunk));

    const results = await Promise.all(promises);
    return this.mergeResults(results);
  }

  processChunkInWorker(chunk) {
    return new Promise((resolve, reject) =&gt; {
      const taskId = this.generateTaskId();

      this.worker.postMessage({
        taskId,
        data: chunk,
        operation: &quot;processExpertData&quot;,
      });

      this.worker.addEventListener(&quot;message&quot;, (event) =&gt; {
        if (event.data.taskId === taskId) {
          resolve(event.data.result);
        }
      });
    });
  }
}
</code></pre>
<h3>12.3 跨平台兼容性</h3>
<h4>12.3.1 问题描述</h4>
<p>需要确保应用在不同操作系统（Windows、macOS、Linux）上的一致性表现。</p>
<h4>12.3.2 解决方案</h4>
<pre><code class="language-javascript">// 平台适配管理器
class PlatformAdapter {
  constructor() {
    this.platform = this.detectPlatform();
    this.adaptations = this.loadPlatformAdaptations();
  }

  detectPlatform() {
    const userAgent = navigator.userAgent;
    if (userAgent.includes(&quot;Win&quot;)) return &quot;windows&quot;;
    if (userAgent.includes(&quot;Mac&quot;)) return &quot;macos&quot;;
    if (userAgent.includes(&quot;Linux&quot;)) return &quot;linux&quot;;
    return &quot;unknown&quot;;
  }

  loadPlatformAdaptations() {
    return {
      windows: {
        filePathSeparator: &quot;\\&quot;,
        defaultFont: &quot;Microsoft YaHei&quot;,
        keyboardShortcuts: { save: &quot;Ctrl+S&quot; },
      },
      macos: {
        filePathSeparator: &quot;/&quot;,
        defaultFont: &quot;PingFang SC&quot;,
        keyboardShortcuts: { save: &quot;Cmd+S&quot; },
      },
      linux: {
        filePathSeparator: &quot;/&quot;,
        defaultFont: &quot;Noto Sans CJK SC&quot;,
        keyboardShortcuts: { save: &quot;Ctrl+S&quot; },
      },
    };
  }

  getAdaptation(key) {
    return (
      this.adaptations[this.platform]?.[key] || this.adaptations.windows[key]
    );
  }
}
</code></pre>
<h2>13. 测试策略</h2>
<h3>13.1 测试架构设计</h3>
<div class="language-mermaid">graph TD
    subgraph &quot;单元测试层&quot;
        A[算法测试] --&gt; A1[Fisher-Yates算法]
        A --&gt; A2[公平性算法]
        A --&gt; A3[数据解析算法]
    end

    subgraph &quot;集成测试层&quot;
        B[模块集成测试] --&gt; B1[数据流测试]
        B --&gt; B2[界面交互测试]
        B --&gt; B3[文件处理测试]
    end

    subgraph &quot;端到端测试层&quot;
        C[用户场景测试] --&gt; C1[完整抽取流程]
        C --&gt; C2[错误处理流程]
        C --&gt; C3[性能压力测试]
    end

    subgraph &quot;兼容性测试层&quot;
        D[平台兼容性] --&gt; D1[Windows测试]
        D --&gt; D2[macOS测试]
        D --&gt; D3[Linux测试]
    end
</div>
<h3>13.2 核心算法测试</h3>
<h4>13.2.1 随机性测试</h4>
<pre><code class="language-javascript">// Fisher-Yates算法随机性测试
describe(&quot;Fisher-Yates Algorithm Randomness&quot;, () =&gt; {
  test(&quot;should produce uniform distribution&quot;, () =&gt; {
    const testArray = [&quot;A&quot;, &quot;B&quot;, &quot;C&quot;, &quot;D&quot;, &quot;E&quot;];
    const iterations = 10000;
    const positionCounts = {};

    // 初始化计数器
    testArray.forEach((item, index) =&gt; {
      positionCounts[item] = new Array(testArray.length).fill(0);
    });

    // 执行多次洗牌
    for (let i = 0; i &lt; iterations; i++) {
      const shuffled = getRandomKeys(testArray, testArray.length);
      shuffled.forEach((item, position) =&gt; {
        positionCounts[item][position]++;
      });
    }

    // 验证分布均匀性（卡方检验）
    const expectedFrequency = iterations / testArray.length;
    const chiSquare = calculateChiSquare(positionCounts, expectedFrequency);
    const criticalValue = 16.919; // 自由度为12，显著性水平0.05

    expect(chiSquare).toBeLessThan(criticalValue);
  });
});
</code></pre>
<h4>13.2.2 公平性测试</h4>
<pre><code class="language-javascript">// 公平性算法测试
describe(&quot;Fairness Algorithm&quot;, () =&gt; {
  test(&quot;should balance expert selection over time&quot;, () =&gt; {
    const experts = [&quot;Expert1&quot;, &quot;Expert2&quot;, &quot;Expert3&quot;, &quot;Expert4&quot;, &quot;Expert5&quot;];
    const selectionCount = {};
    experts.forEach((expert) =&gt; (selectionCount[expert] = 0));

    // 模拟100次抽取
    for (let i = 0; i &lt; 100; i++) {
      const selected = performFairSelection(experts, 3);
      selected.forEach((expert) =&gt; selectionCount[expert]++);
    }

    // 计算标准差，验证分布均匀性
    const counts = Object.values(selectionCount);
    const mean = counts.reduce((a, b) =&gt; a + b) / counts.length;
    const variance =
      counts.reduce((sum, count) =&gt; sum + Math.pow(count - mean, 2), 0) /
      counts.length;
    const standardDeviation = Math.sqrt(variance);

    // 标准差应该相对较小，表示分布均匀
    expect(standardDeviation).toBeLessThan(mean * 0.2);
  });
});
</code></pre>
<h3>13.3 性能测试</h3>
<h4>13.3.1 压力测试</h4>
<pre><code class="language-javascript">// 大数据量性能测试
describe(&quot;Performance Tests&quot;, () =&gt; {
  test(&quot;should handle large datasets efficiently&quot;, async () =&gt; {
    // 生成大量测试数据
    const largeDataset = generateTestData(10000, 500); // 10000个答辩人，500个评委

    const startTime = performance.now();

    // 执行数据处理
    await processLargeDataset(largeDataset);

    const endTime = performance.now();
    const executionTime = endTime - startTime;

    // 验证处理时间在可接受范围内
    expect(executionTime).toBeLessThan(5000); // 5秒内完成
  });

  test(&quot;should maintain memory usage within limits&quot;, () =&gt; {
    const initialMemory = performance.memory.usedJSHeapSize;

    // 执行内存密集型操作
    performMemoryIntensiveOperations();

    // 强制垃圾回收
    if (global.gc) {
      global.gc();
    }

    const finalMemory = performance.memory.usedJSHeapSize;
    const memoryIncrease = finalMemory - initialMemory;

    // 验证内存增长在合理范围内
    expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // 50MB
  });
});
</code></pre>
<h2>14. 部署和分发</h2>
<h3>14.1 自动化构建流程</h3>
<div class="language-mermaid">flowchart TD
    A[代码提交] --&gt; B[CI/CD触发]
    B --&gt; C[代码检查]
    C --&gt; D[单元测试]
    D --&gt; E[集成测试]
    E --&gt; F[构建应用]
    F --&gt; G[平台打包]

    subgraph &quot;多平台构建&quot;
        G --&gt; H1[Windows构建]
        G --&gt; H2[macOS构建]
        G --&gt; H3[Linux构建]
    end

    H1 --&gt; I[签名验证]
    H2 --&gt; I
    H3 --&gt; I
    I --&gt; J[发布准备]
    J --&gt; K[版本发布]
</div>
<h3>14.2 安装包配置</h3>
<h4>14.2.1 Windows 安装包配置</h4>
<pre><code class="language-javascript">// electron-builder Windows配置
const windowsConfig = {
  target: {
    target: &quot;nsis&quot;,
    arch: [&quot;x64&quot;, &quot;ia32&quot;],
  },
  icon: &quot;build/icon.ico&quot;,
  requestedExecutionLevel: &quot;asInvoker&quot;,
  artifactName: &quot;${productName}-${version}-${arch}.${ext}&quot;,
  nsis: {
    oneClick: false,
    allowElevation: true,
    allowToChangeInstallationDirectory: true,
    installerIcon: &quot;build/installer.ico&quot;,
    uninstallerIcon: &quot;build/uninstaller.ico&quot;,
    installerHeaderIcon: &quot;build/installerHeader.ico&quot;,
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: &quot;随机抽取系统&quot;,
  },
};
</code></pre>
<h3>14.3 更新机制</h3>
<h4>14.3.1 自动更新实现</h4>
<pre><code class="language-javascript">// 自动更新管理器
class AutoUpdater {
    constructor() {
        this.updateServer = 'https://updates.example.com';
        this.currentVersion = app.getVersion();
        this.checkInterval = 24 * 60 * 60 * 1000; // 24小时
    }

    async checkForUpdates() {
        try {
            const response = await fetch(`${this.updateServer}/latest`);
            const updateInfo = await response.json();

            if (this.isNewerVersion(updateInfo.version)) {
                return this.promptForUpdate(updateInfo);
            }

            return { hasUpdate: false };
        } catch (error) {
            console.error('检查更新失败:', error);
            return { hasUpdate: false, error };
        }
    }

    isNewerVersion(rem
</code></pre>
